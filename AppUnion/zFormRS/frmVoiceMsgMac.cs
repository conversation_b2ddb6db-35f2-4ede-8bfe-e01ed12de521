﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Diagnostics;
using System.Runtime.InteropServices;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;
using iTong.Android.Wave;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using iTong.CoreCefSharp;
#endif

namespace iTong.CoreModule
{
    public partial class frmVoiceMsg
    {
        private skLabel lblUserDetail;
        private skButton lblUserName;
        private skPictureBox picUserImage;
        private skButton btnAgree;
        private skButton btnReject;

        private void InitializeComponent()
        {
            this.lblUserDetail = new skLabel();
            this.lblUserName = new skButton();
            this.picUserImage = new skPictureBox();
            this.btnAgree = new skButton();
            this.btnReject = new skButton();
            this.SuspendLayout();

            // macOS坐标系统转换：Y = 窗体高度(112) - Windows_Y - 控件高度
            // 原Windows坐标 -> macOS坐标转换
            //
            // lblUserDetail
            //
            this.lblUserDetail.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            // 原Windows坐标(64, 37) -> macOS坐标(64, 112-37-24=51)
            this.lblUserDetail.Frame = new Rectangle(64, 51, 294, 24);
            this.lblUserDetail.IsSuspendLayout = false;
            this.lblUserDetail.Location = new Point(64, 51);
            this.lblUserDetail.Name = "lblUserDetail";
            this.lblUserDetail.Size = new Size(294, 24);
            this.lblUserDetail.skAutoHeight = false;
            this.lblUserDetail.skAutoSize = false;
            this.lblUserDetail.skAutoSizeByBackgroudImage = false;
            this.lblUserDetail.skAutoWidth = false;
            this.lblUserDetail.skBackgroundColor = skColor.Transparent;
            this.lblUserDetail.skBackgroundColorShadow = null;
            this.lblUserDetail.skBackgroundImage = null;
            this.lblUserDetail.skBackgroundImageAlign = skImageAlignment.None;
            this.lblUserDetail.skBackgroundImageState = skImageState.OneState;
            this.lblUserDetail.skBackgroundImageStretch = false;
            this.lblUserDetail.skBorderCornerColor = null;
            this.lblUserDetail.skBorderDashLine = null;
            this.lblUserDetail.skBorderDashStyle = DashStyle.Dash;
            this.lblUserDetail.skBorderFillColor = null;
            this.lblUserDetail.skBorderFillColorDown = null;
            this.lblUserDetail.skBorderFillColorHover = null;
            this.lblUserDetail.skBorderPadding = new Padding(0);
            this.lblUserDetail.skBorderStrokeColor = skColor.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.lblUserDetail.skBorderStrokeColorDown = null;
            this.lblUserDetail.skBorderStrokeColorHover = null;
            this.lblUserDetail.skBorderType = skBorderType.None;
            this.lblUserDetail.skBorderWidth = 1;
            this.lblUserDetail.skCanAction = true;
            this.lblUserDetail.skCaretLocation = new Point(0, 0);
            this.lblUserDetail.skCaretSize = new Size(0, 0);
            this.lblUserDetail.skDescription = null;
            this.lblUserDetail.skLineBreakMode = skNSLineBreakMode.TailTruncation;
            //this.lblUserDetail.skLineSpace = -2;
            this.lblUserDetail.skMouseStateCustom = skMouseState.MouseLeave;
            this.lblUserDetail.skReplaceToolTip = true;
            this.lblUserDetail.skRowDiff = 0;
            this.lblUserDetail.skShowCaret = false;
            this.lblUserDetail.skShowLineCount = -1;
            this.lblUserDetail.skSplitString = "0,0,0,0";
            this.lblUserDetail.skSuperviewReceiveMessage = true;
            this.lblUserDetail.skTag = null;
            this.lblUserDetail.skTagEx = null;
            this.lblUserDetail.skText = "Requesting to voice chat with you";
            this.lblUserDetail.skTextAlign = ContentAlignment.MiddleLeft;
            this.lblUserDetail.skTextBoldColor = Color.Black;
            this.lblUserDetail.skTextColor = skColor.FromArgb(((int)(((byte)(164)))), ((int)(((byte)(168)))), ((int)(((byte)(179)))));
            this.lblUserDetail.skTextColorCheck = Color.Black;
            this.lblUserDetail.skTextColorCheckDisable = Color.Gray;
            this.lblUserDetail.skTextColorCheckDown = Color.Black;
            this.lblUserDetail.skTextColorCheckHover = Color.Black;
            this.lblUserDetail.skTextColorDisable = skColor.FromArgb(((int)(((byte)(164)))), ((int)(((byte)(168)))), ((int)(((byte)(179)))));
            this.lblUserDetail.skTextColorDown = skColor.FromArgb(((int)(((byte)(164)))), ((int)(((byte)(168)))), ((int)(((byte)(179)))));
            this.lblUserDetail.skTextColorHover = skColor.FromArgb(((int)(((byte)(164)))), ((int)(((byte)(168)))), ((int)(((byte)(179)))));
            this.lblUserDetail.skTextFont = MyFont.CreateFont("微软雅黑", 8.25F);
            this.lblUserDetail.skTextItalicBoldColor = Color.Black;
            this.lblUserDetail.skTextItalicColor = Color.Black;
            this.lblUserDetail.skToolTip = "";
            this.lblUserDetail.skTriangleHeight = 5;
            this.lblUserDetail.skTrianglePlaceCorner = 15;
            this.lblUserDetail.skTrianglePosition = skTrianglePosition.None;
            this.lblUserDetail.skTriangleWidth = 12;
            this.lblUserDetail.skViewParas = null;
            this.lblUserDetail.skViewTypes = null;
            this.lblUserDetail.TabIndex = 17;
            this.lblUserDetail.Text = "skLabel1";
            //
            // lblUserName
            //
            this.lblUserName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            // 原Windows坐标(64, 16) -> macOS坐标(64, 112-16-20=76)
            this.lblUserName.Frame = new Rectangle(64, 76, 226, 20);
            this.lblUserName.IsSuspendLayout = false;
            this.lblUserName.Location = new Point(64, 76);
            this.lblUserName.Name = "lblUserName";
            this.lblUserName.Size = new Size(226, 20);
            this.lblUserName.skAdriftIconWhenHover = false;
            this.lblUserName.skAutoHeight = false;
            this.lblUserName.skAutoSize = false;
            this.lblUserName.skAutoSizeByBackgroudImage = false;
            this.lblUserName.skBackgroundColor = skColor.Transparent;
            this.lblUserName.skBackgroundColorCheck = Color.White;
            this.lblUserName.skBackgroundColorShadow = null;
            this.lblUserName.skBackgroundImage = null;
            this.lblUserName.skBackgroundImageAlign = skImageAlignment.None;
            this.lblUserName.skBackgroundImageCheck = null;
            this.lblUserName.skBackgroundImageCheckDown = null;
            this.lblUserName.skBackgroundImageCheckHover = null;
            this.lblUserName.skBackgroundImageCheckLeave = null;
            this.lblUserName.skBackgroundImageDown = null;
            this.lblUserName.skBackgroundImageHover = null;
            this.lblUserName.skBackgroundImageLeave = null;
            this.lblUserName.skBackgroundImageState = skImageState.OneState;
            this.lblUserName.skBackgroundImageStretch = false;
            this.lblUserName.skBadgeBackgroundColor = null;
            this.lblUserName.skBadgeBackgroundColorSelect = null;
            this.lblUserName.skBadgeBackgroundDiff = 3;
            this.lblUserName.skBadgeColor = Color.Black;
            this.lblUserName.skBadgeColorSelect = null;
            this.lblUserName.skBadgeFont = MyFont.CreateFont("微软雅黑", 12.8F);
            this.lblUserName.skBadgeImage = null;
            this.lblUserName.skBadgeNumber = 0;
            this.lblUserName.skBadgeNumberOffset = new Point(0, 0);
            this.lblUserName.skBadgeNumberString = "";
            this.lblUserName.skBorderCornerColor = null;
            this.lblUserName.skBorderDashLine = null;
            this.lblUserName.skBorderDashStyle = DashStyle.Dash;
            this.lblUserName.skBorderFillColor = null;
            this.lblUserName.skBorderFillColorDown = null;
            this.lblUserName.skBorderFillColorHover = null;
            this.lblUserName.skBorderPadding = new Padding(0);
            this.lblUserName.skBorderStrokeColor = skColor.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.lblUserName.skBorderStrokeColorDown = null;
            this.lblUserName.skBorderStrokeColorHover = null;
            this.lblUserName.skBorderType = skBorderType.None;
            this.lblUserName.skBorderWidth = 1;
            this.lblUserName.skButtonStyle = skButtonStyle.Normal;
            this.lblUserName.skButtonType = skButtonType.None;
            this.lblUserName.skCanAction = true;
            this.lblUserName.skCanCheckedCancel = true;
            this.lblUserName.skCaretLocation = new Point(0, 0);
            this.lblUserName.skCaretSize = new Size(0, 0);
            this.lblUserName.skChecked = false;
            this.lblUserName.skDescription = null;
            this.lblUserName.skEndEllipsis = false;
            this.lblUserName.skGroupName = "";
            this.lblUserName.skIcon = null;
            this.lblUserName.skIconAlign = skImageAlignment.Center;
            this.lblUserName.skIconBeforeText = true;
            this.lblUserName.skIconBorderFillColor = null;
            this.lblUserName.skIconBorderFillColorDown = null;
            this.lblUserName.skIconBorderFillColorHover = null;
            this.lblUserName.skIconBorderStrokeColor = Color.Gray;
            this.lblUserName.skIconBorderStrokeColorDown = null;
            this.lblUserName.skIconBorderStrokeColorHover = null;
            this.lblUserName.skIconCheck = null;
            this.lblUserName.skIconCheckDown = null;
            this.lblUserName.skIconCheckHover = null;
            this.lblUserName.skIconCheckLeave = null;
            this.lblUserName.skIconClose = null;
            this.lblUserName.skIconCloseSize = new Size(0, 0);
            this.lblUserName.skIconCloseState = skImageState.OneState;
            this.lblUserName.skIconCorner = null;
            this.lblUserName.skIconCornerAlign = skImageAlignment.BottomRight;
            this.lblUserName.skIconCornerOffset = new Point(0, 0);
            this.lblUserName.skIconCornerSize = new Size(0, 0);
            this.lblUserName.skIconDown = null;
            this.lblUserName.skIconHover = null;
            this.lblUserName.skIconLeave = null;
            this.lblUserName.skIconMore = null;
            this.lblUserName.skIconMoreAfterText = false;
            this.lblUserName.skIconMoreAlign = skImageAlignment.Right;
            this.lblUserName.skIconMoreDashLine = null;
            this.lblUserName.skIconMoreExpand = null;
            this.lblUserName.skIconMoreLineColor = null;
            this.lblUserName.skIconMoreLineColorHover = null;
            this.lblUserName.skIconMoreLineStyle = skLineStyle.None;
            this.lblUserName.skIconMoreLineWidth = 1;
            this.lblUserName.skIconMoreSize = new Size(8, 14);
            this.lblUserName.skIconMoreState = skImageState.OneState;
            this.lblUserName.skIconOffset = new Point(0, 0);
            this.lblUserName.skIconPlaceText = 2;
            this.lblUserName.skIconReadOnly = null;
            this.lblUserName.skIconReadOnlyShadow = null;
            this.lblUserName.skIconShadow = null;
            this.lblUserName.skIconShadowOffset = new Point(0, 0);
            this.lblUserName.skIconShadowSize = new Size(0, 0);
            this.lblUserName.skIconShadowSplit = "10,10,10,10";
            this.lblUserName.skIconSize = new Size(0, 0);
            this.lblUserName.skIconState = skImageState.OneState;
            this.lblUserName.skIconToolTip = null;
            this.lblUserName.skIconToolTipPlaceText = 3;
            this.lblUserName.skIconToolTipSize = new Size(0, 0);
            this.lblUserName.skIsLinkLabel = false;
            this.lblUserName.skLineBreakMode = skNSLineBreakMode.TailTruncation;
            this.lblUserName.skLineColor = null;
            this.lblUserName.skLineHeight = 6;
            this.lblUserName.skLineWidth = 30;
            this.lblUserName.skMorePlaceText = 5;
            this.lblUserName.skMouseStateCustom = skMouseState.MouseLeave;
            this.lblUserName.skMultiLine = false;
            this.lblUserName.skReadOnly = false;
            this.lblUserName.skRedDot = null;
            this.lblUserName.skReplaceToolTip = true;
            this.lblUserName.skShadow = false;
            this.lblUserName.skShadowColor = skColor.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblUserName.skShadowOffset = new Point(1, 1);
            this.lblUserName.skShowCaret = false;
            this.lblUserName.skShowIcon = true;
            this.lblUserName.skShowIconClose = false;
            this.lblUserName.skShowIconMore = false;
            this.lblUserName.skShowIconShadow = false;
            this.lblUserName.skShowLineWhenCheck = false;
            this.lblUserName.skShowNew = false;
            this.lblUserName.skShowRedDot = false;
            this.lblUserName.skShowToolTipOnButton = true;
            this.lblUserName.skSplitString = "0,0,0,0";
            this.lblUserName.skSuperviewReceiveMessage = true;
            this.lblUserName.skTag = null;
            this.lblUserName.skTagEx = null;
            this.lblUserName.skText = "加入到企业";
            this.lblUserName.skTextAlign = ContentAlignment.MiddleLeft;
            this.lblUserName.skTextColor = skColor.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(47)))), ((int)(((byte)(51)))));
            this.lblUserName.skTextColorCheck = Color.Black;
            this.lblUserName.skTextColorCheckDisable = Color.Gray;
            this.lblUserName.skTextColorCheckDown = Color.Black;
            this.lblUserName.skTextColorCheckHover = Color.Black;
            this.lblUserName.skTextColorDisable = Color.Gray;
            this.lblUserName.skTextColorDown = skColor.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(47)))), ((int)(((byte)(51)))));
            this.lblUserName.skTextColorHover = skColor.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(47)))), ((int)(((byte)(51)))));
            this.lblUserName.skTextFont = MyFont.CreateFont("微软雅黑", 10.5F, true);
            this.lblUserName.skTextMouseDownPlace = 0;
            this.lblUserName.skTextPlaceTip = 3;
            this.lblUserName.skTextTemp = null;
            this.lblUserName.skTheSameWidthOfTextAndIcon = false;
            this.lblUserName.skTimerCountdownNumber = 0;
            this.lblUserName.skToolTip = "";
            this.lblUserName.skToolTipAlign = ContentAlignment.MiddleCenter;
            this.lblUserName.skToolTipBackgroundColor = null;
            this.lblUserName.skToolTipCheck = "";
            this.lblUserName.skToolTipColor = Color.Gray;
            this.lblUserName.skToolTipFont = MyFont.CreateFont("微软雅黑", 10F);
            this.lblUserName.skTriangleHeight = 5;
            this.lblUserName.skTrianglePlaceCorner = 15;
            this.lblUserName.skTrianglePosition = skTrianglePosition.None;
            this.lblUserName.skTriangleWidth = 12;
            this.lblUserName.skUpToDownWhenCenter = true;
            this.lblUserName.skUseDefaultIcon = false;
            this.lblUserName.skViewParas = null;
            this.lblUserName.skViewTypes = null;
            this.lblUserName.TabIndex = 16;
            this.lblUserName.Text = "加入到企业";
            //
            // picUserImage
            //
            // 原Windows坐标(20, 16) -> macOS坐标(20, 112-16-36=60)
            this.picUserImage.Frame = new Rectangle(20, 60, 36, 36);
            this.picUserImage.Image = MyResource.GetImage("rs_user_head.png");
            this.picUserImage.IsSuspendLayout = false;
            this.picUserImage.Location = new Point(20, 60);
            this.picUserImage.Name = "picUserImage";
            this.picUserImage.Size = new Size(36, 36);
            this.picUserImage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.picUserImage.skAutoSize = false;
            this.picUserImage.skAutoSizeByBackgroudImage = false;
            this.picUserImage.skBackgroundColor = Color.White;
            this.picUserImage.skBackgroundColorShadow = null;
            this.picUserImage.skBackgroundImage = null;
            this.picUserImage.skBackgroundImageAlign = skImageAlignment.None;
            this.picUserImage.skBackgroundImageState = skImageState.OneState;
            this.picUserImage.skBackgroundImageStretch = false;
            this.picUserImage.skBorderCornerColor = null;
            this.picUserImage.skBorderDashLine = null;
            this.picUserImage.skBorderDashStyle = DashStyle.Dash;
            this.picUserImage.skBorderFillColor = null;
            this.picUserImage.skBorderFillColorDown = null;
            this.picUserImage.skBorderFillColorHover = null;
            this.picUserImage.skBorderPadding = new Padding(0);
            this.picUserImage.skBorderStrokeColor = skColor.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.picUserImage.skBorderStrokeColorDown = null;
            this.picUserImage.skBorderStrokeColorHover = null;
            this.picUserImage.skBorderType = skBorderType.None;
            this.picUserImage.skBorderWidth = 1;
            this.picUserImage.skCanAction = true;
            this.picUserImage.skCaretLocation = new Point(0, 0);
            this.picUserImage.skCaretSize = new Size(0, 0);
            this.picUserImage.skDescription = null;
            this.picUserImage.skLineBreakMode = skNSLineBreakMode.TailTruncation;
            this.picUserImage.skMouseStateCustom = skMouseState.MouseLeave;
            this.picUserImage.skReplaceToolTip = true;
            this.picUserImage.skShowCaret = false;
            this.picUserImage.skSplitString = "0,0,0,0";
            this.picUserImage.skSuperviewReceiveMessage = true;
            this.picUserImage.skTag = null;
            this.picUserImage.skTagEx = null;
            this.picUserImage.skToolTip = "";
            this.picUserImage.skTriangleHeight = 5;
            this.picUserImage.skTrianglePlaceCorner = 15;
            this.picUserImage.skTrianglePosition = skTrianglePosition.None;
            this.picUserImage.skTriangleWidth = 12;
            this.picUserImage.skViewParas = null;
            this.picUserImage.skViewTypes = null;
            this.picUserImage.TabIndex = 15;
            //
            // btnAgree
            //
            this.btnAgree.Cursor = System.Windows.Forms.Cursors.Hand;
            // 原Windows坐标(285, 68) -> macOS坐标(285, 112-68-28=16)
            this.btnAgree.Frame = new Rectangle(285, 16, 69, 28);
            this.btnAgree.IsSuspendLayout = false;
            this.btnAgree.Location = new Point(285, 16);
            this.btnAgree.Name = "btnAgree";
            this.btnAgree.Size = new Size(69, 28);
            this.btnAgree.skAdriftIconWhenHover = false;
            this.btnAgree.skAutoHeight = false;
            this.btnAgree.skAutoSize = false;
            this.btnAgree.skAutoSizeByBackgroudImage = false;
            this.btnAgree.skBackgroundColor = Color.White;
            this.btnAgree.skBackgroundColorCheck = Color.White;
            this.btnAgree.skBackgroundColorShadow = null;
            this.btnAgree.skBackgroundImage = MyResource.GetImage("rs_btn_green_4.png");
            this.btnAgree.skBackgroundImageAlign = skImageAlignment.None;
            this.btnAgree.skBackgroundImageCheck = null;
            this.btnAgree.skBackgroundImageCheckDown = null;
            this.btnAgree.skBackgroundImageCheckHover = null;
            this.btnAgree.skBackgroundImageCheckLeave = null;
            this.btnAgree.skBackgroundImageDown = null;
            this.btnAgree.skBackgroundImageHover = null;
            this.btnAgree.skBackgroundImageLeave = null;
            this.btnAgree.skBackgroundImageState = skImageState.FourState;
            this.btnAgree.skBackgroundImageStretch = false;
            this.btnAgree.skBadgeBackgroundColor = null;
            this.btnAgree.skBadgeBackgroundColorSelect = null;
            this.btnAgree.skBadgeBackgroundDiff = 3;
            this.btnAgree.skBadgeColor = Color.Black;
            this.btnAgree.skBadgeColorSelect = null;
            this.btnAgree.skBadgeFont = MyFont.CreateFont("微软雅黑", 10F);
            this.btnAgree.skBadgeImage = null;
            this.btnAgree.skBadgeNumber = 0;
            this.btnAgree.skBadgeNumberOffset = new Point(0, 0);
            this.btnAgree.skBadgeNumberString = "";
            this.btnAgree.skBorderCornerColor = null;
            this.btnAgree.skBorderDashLine = null;
            this.btnAgree.skBorderDashStyle = DashStyle.Dash;
            this.btnAgree.skBorderFillColor = null;
            this.btnAgree.skBorderFillColorDown = null;
            this.btnAgree.skBorderFillColorHover = null;
            this.btnAgree.skBorderPadding = new Padding(0);
            this.btnAgree.skBorderStrokeColor = skColor.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.btnAgree.skBorderStrokeColorDown = null;
            this.btnAgree.skBorderStrokeColorHover = null;
            this.btnAgree.skBorderType = skBorderType.None;
            this.btnAgree.skBorderWidth = 1;
            this.btnAgree.skButtonStyle = skButtonStyle.Normal;
            this.btnAgree.skButtonType = skButtonType.None;
            this.btnAgree.skCanAction = true;
            this.btnAgree.skCanCheckedCancel = true;
            this.btnAgree.skCaretLocation = new Point(0, 0);
            this.btnAgree.skCaretSize = new Size(0, 0);
            this.btnAgree.skChecked = false;
            this.btnAgree.skDescription = null;
            this.btnAgree.skEndEllipsis = false;
            this.btnAgree.skGroupName = "";
            this.btnAgree.skIcon = MyResource.GetImage("rs_voip_connect.png");
            this.btnAgree.skIconAlign = skImageAlignment.Center;
            this.btnAgree.skIconBeforeText = true;
            this.btnAgree.skIconBorderFillColor = null;
            this.btnAgree.skIconBorderFillColorDown = null;
            this.btnAgree.skIconBorderFillColorHover = null;
            this.btnAgree.skIconBorderStrokeColor = Color.Gray;
            this.btnAgree.skIconBorderStrokeColorDown = null;
            this.btnAgree.skIconBorderStrokeColorHover = null;
            this.btnAgree.skIconCheck = null;
            this.btnAgree.skIconCheckDown = null;
            this.btnAgree.skIconCheckHover = null;
            this.btnAgree.skIconCheckLeave = null;
            this.btnAgree.skIconClose = null;
            this.btnAgree.skIconCloseSize = new Size(0, 0);
            this.btnAgree.skIconCloseState = skImageState.OneState;
            this.btnAgree.skIconCorner = null;
            this.btnAgree.skIconCornerAlign = skImageAlignment.BottomRight;
            this.btnAgree.skIconCornerOffset = new Point(0, 0);
            this.btnAgree.skIconCornerSize = new Size(0, 0);
            this.btnAgree.skIconDown = null;
            this.btnAgree.skIconHover = null;
            this.btnAgree.skIconLeave = null;
            this.btnAgree.skIconMore = null;
            this.btnAgree.skIconMoreAfterText = false;
            this.btnAgree.skIconMoreAlign = skImageAlignment.Right;
            this.btnAgree.skIconMoreDashLine = null;
            this.btnAgree.skIconMoreExpand = null;
            this.btnAgree.skIconMoreLineColor = null;
            this.btnAgree.skIconMoreLineColorHover = null;
            this.btnAgree.skIconMoreLineStyle = skLineStyle.None;
            this.btnAgree.skIconMoreLineWidth = 1;
            this.btnAgree.skIconMoreSize = new Size(8, 14);
            this.btnAgree.skIconMoreState = skImageState.OneState;
            this.btnAgree.skIconOffset = new Point(0, -1);
            this.btnAgree.skIconPlaceText = 2;
            this.btnAgree.skIconReadOnly = null;
            this.btnAgree.skIconReadOnlyShadow = null;
            this.btnAgree.skIconShadow = null;
            this.btnAgree.skIconShadowOffset = new Point(0, 0);
            this.btnAgree.skIconShadowSize = new Size(0, 0);
            this.btnAgree.skIconShadowSplit = "10,10,10,10";
            this.btnAgree.skIconSize = new Size(16, 17);
            this.btnAgree.skIconState = skImageState.OneState;
            this.btnAgree.skIconToolTip = null;
            this.btnAgree.skIconToolTipPlaceText = 3;
            this.btnAgree.skIconToolTipSize = new Size(0, 0);
            this.btnAgree.skIsLinkLabel = false;
            this.btnAgree.skLineBreakMode = skNSLineBreakMode.TailTruncation;
            this.btnAgree.skLineColor = null;
            this.btnAgree.skLineHeight = 6;
            this.btnAgree.skLineWidth = 30;
            this.btnAgree.skMorePlaceText = 5;
            this.btnAgree.skMouseStateCustom = skMouseState.MouseLeave;
            this.btnAgree.skMultiLine = false;
            this.btnAgree.skReadOnly = false;
            this.btnAgree.skRedDot = null;
            this.btnAgree.skReplaceToolTip = true;
            this.btnAgree.skShadow = false;
            this.btnAgree.skShadowColor = skColor.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnAgree.skShadowOffset = new Point(1, 1);
            this.btnAgree.skShowCaret = false;
            this.btnAgree.skShowIcon = true;
            this.btnAgree.skShowIconClose = false;
            this.btnAgree.skShowIconMore = false;
            this.btnAgree.skShowIconShadow = false;
            this.btnAgree.skShowLineWhenCheck = false;
            this.btnAgree.skShowNew = false;
            this.btnAgree.skShowRedDot = false;
            this.btnAgree.skShowToolTipOnButton = false;
            this.btnAgree.skSplitString = "12,5,12,5";
            this.btnAgree.skSuperviewReceiveMessage = true;
            this.btnAgree.skTag = null;
            this.btnAgree.skTagEx = null;
            this.btnAgree.skText = "";
            this.btnAgree.skTextAlign = ContentAlignment.MiddleCenter;
            this.btnAgree.skTextColor = Color.White;
            this.btnAgree.skTextColorCheck = Color.Black;
            this.btnAgree.skTextColorCheckDisable = Color.Gray;
            this.btnAgree.skTextColorCheckDown = Color.Black;
            this.btnAgree.skTextColorCheckHover = Color.Black;
            this.btnAgree.skTextColorDisable = Color.Gray;
            this.btnAgree.skTextColorDown = Color.White;
            this.btnAgree.skTextColorHover = Color.White;
            this.btnAgree.skTextFont = MyFont.CreateFont("微软雅黑", 10.5F);
            this.btnAgree.skTextMouseDownPlace = 0;
            this.btnAgree.skTextPlaceTip = 3;
            this.btnAgree.skTextTemp = null;
            this.btnAgree.skTheSameWidthOfTextAndIcon = false;
            this.btnAgree.skTimerCountdownNumber = 0;
            this.btnAgree.skToolTip = "";
            this.btnAgree.skToolTipAlign = ContentAlignment.MiddleCenter;
            this.btnAgree.skToolTipBackgroundColor = null;
            this.btnAgree.skToolTipCheck = "";
            this.btnAgree.skToolTipColor = Color.Gray;
            this.btnAgree.skToolTipFont = MyFont.CreateFont("微软雅黑", 7F);
            this.btnAgree.skTriangleHeight = 5;
            this.btnAgree.skTrianglePlaceCorner = 15;
            this.btnAgree.skTrianglePosition = skTrianglePosition.None;
            this.btnAgree.skTriangleWidth = 12;
            this.btnAgree.skUpToDownWhenCenter = true;
            this.btnAgree.skUseDefaultIcon = false;
            this.btnAgree.skViewParas = null;
            this.btnAgree.skViewTypes = null;
            this.btnAgree.TabIndex = 18;
            this.btnAgree.Click += new System.EventHandler(this.btnAgree_Click);
            //
            // btnReject
            //
            this.btnReject.Cursor = System.Windows.Forms.Cursors.Hand;
            // 原Windows坐标(206, 68) -> macOS坐标(206, 112-68-28=16)
            this.btnReject.Frame = new Rectangle(206, 16, 69, 28);
            this.btnReject.IsSuspendLayout = false;
            this.btnReject.Location = new Point(206, 16);
            this.btnReject.Name = "btnReject";
            this.btnReject.Padding = new Padding(12, 0, 0, 2);
            this.btnReject.Size = new Size(69, 28);
            this.btnReject.skAdriftIconWhenHover = false;
            this.btnReject.skAutoHeight = false;
            this.btnReject.skAutoSize = false;
            this.btnReject.skAutoSizeByBackgroudImage = false;
            this.btnReject.skBackgroundColor = skColor.Transparent;
            this.btnReject.skBackgroundColorCheck = skColor.Transparent;
            this.btnReject.skBackgroundColorShadow = null;
            this.btnReject.skBackgroundImage = MyResource.GetImage("rs_btn_red_4.png");
            this.btnReject.skBackgroundImageAlign = skImageAlignment.Center;
            this.btnReject.skBackgroundImageCheck = null;
            this.btnReject.skBackgroundImageCheckDown = null;
            this.btnReject.skBackgroundImageCheckHover = null;
            this.btnReject.skBackgroundImageCheckLeave = null;
            this.btnReject.skBackgroundImageDown = null;
            this.btnReject.skBackgroundImageHover = null;
            this.btnReject.skBackgroundImageLeave = null;
            this.btnReject.skBackgroundImageState = skImageState.FourState;
            this.btnReject.skBackgroundImageStretch = false;
            this.btnReject.skBadgeBackgroundColor = null;
            this.btnReject.skBadgeBackgroundColorSelect = null;
            this.btnReject.skBadgeBackgroundDiff = 3;
            this.btnReject.skBadgeColor = Color.Black;
            this.btnReject.skBadgeColorSelect = null;
            this.btnReject.skBadgeFont = MyFont.CreateFont("微软雅黑", 10F);
            this.btnReject.skBadgeImage = null;
            this.btnReject.skBadgeNumber = 0;
            this.btnReject.skBadgeNumberOffset = new Point(0, 0);
            this.btnReject.skBadgeNumberString = "";
            this.btnReject.skBorderCornerColor = null;
            this.btnReject.skBorderDashLine = null;
            this.btnReject.skBorderDashStyle = DashStyle.Dash;
            this.btnReject.skBorderFillColor = null;
            this.btnReject.skBorderFillColorDown = null;
            this.btnReject.skBorderFillColorHover = null;
            this.btnReject.skBorderPadding = new Padding(0);
            this.btnReject.skBorderStrokeColor = skColor.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.btnReject.skBorderStrokeColorDown = null;
            this.btnReject.skBorderStrokeColorHover = null;
            this.btnReject.skBorderType = skBorderType.None;
            this.btnReject.skBorderWidth = 1;
            this.btnReject.skButtonStyle = skButtonStyle.Normal;
            this.btnReject.skButtonType = skButtonType.None;
            this.btnReject.skCanAction = true;
            this.btnReject.skCanCheckedCancel = true;
            this.btnReject.skCaretLocation = new Point(0, 0);
            this.btnReject.skCaretSize = new Size(0, 0);
            this.btnReject.skChecked = false;
            this.btnReject.skDescription = null;
            this.btnReject.skEndEllipsis = false;
            this.btnReject.skGroupName = "";
            this.btnReject.skIcon = MyResource.GetImage("rs_voip_reject.png");
            this.btnReject.skIconAlign = skImageAlignment.Left;
            this.btnReject.skIconBeforeText = true;
            this.btnReject.skIconBorderFillColor = null;
            this.btnReject.skIconBorderFillColorDown = null;
            this.btnReject.skIconBorderFillColorHover = null;
            this.btnReject.skIconBorderStrokeColor = Color.Gray;
            this.btnReject.skIconBorderStrokeColorDown = null;
            this.btnReject.skIconBorderStrokeColorHover = null;
            this.btnReject.skIconCheck = null;
            this.btnReject.skIconCheckDown = null;
            this.btnReject.skIconCheckHover = null;
            this.btnReject.skIconCheckLeave = null;
            this.btnReject.skIconClose = null;
            this.btnReject.skIconCloseSize = new Size(0, 0);
            this.btnReject.skIconCloseState = skImageState.OneState;
            this.btnReject.skIconCorner = null;
            this.btnReject.skIconCornerAlign = skImageAlignment.BottomRight;
            this.btnReject.skIconCornerOffset = new Point(0, 0);
            this.btnReject.skIconCornerSize = new Size(0, 0);
            this.btnReject.skIconDown = null;
            this.btnReject.skIconHover = null;
            this.btnReject.skIconLeave = null;
            this.btnReject.skIconMore = null;
            this.btnReject.skIconMoreAfterText = false;
            this.btnReject.skIconMoreAlign = skImageAlignment.Right;
            this.btnReject.skIconMoreDashLine = null;
            this.btnReject.skIconMoreExpand = null;
            this.btnReject.skIconMoreLineColor = null;
            this.btnReject.skIconMoreLineColorHover = null;
            this.btnReject.skIconMoreLineStyle = skLineStyle.None;
            this.btnReject.skIconMoreLineWidth = 1;
            this.btnReject.skIconMoreSize = new Size(8, 14);
            this.btnReject.skIconMoreState = skImageState.OneState;
            this.btnReject.skIconOffset = new Point(0, -1);
            this.btnReject.skIconPlaceText = 3;
            this.btnReject.skIconReadOnly = null;
            this.btnReject.skIconReadOnlyShadow = null;
            this.btnReject.skIconShadow = null;
            this.btnReject.skIconShadowOffset = new Point(0, 0);
            this.btnReject.skIconShadowSize = new Size(0, 0);
            this.btnReject.skIconShadowSplit = "10,10,10,10";
            this.btnReject.skIconSize = new Size(16, 17);
            this.btnReject.skIconState = skImageState.OneState;
            this.btnReject.skIconToolTip = null;
            this.btnReject.skIconToolTipPlaceText = 3;
            this.btnReject.skIconToolTipSize = new Size(0, 0);
            this.btnReject.skIsLinkLabel = false;
            this.btnReject.skLineBreakMode = skNSLineBreakMode.TailTruncation;
            this.btnReject.skLineColor = null;
            this.btnReject.skLineHeight = 6;
            this.btnReject.skLineWidth = 30;
            this.btnReject.skMorePlaceText = 5;
            this.btnReject.skMouseStateCustom = skMouseState.MouseLeave;
            this.btnReject.skMultiLine = false;
            this.btnReject.skReadOnly = false;
            this.btnReject.skRedDot = null;
            this.btnReject.skReplaceToolTip = true;
            this.btnReject.skShadow = false;
            this.btnReject.skShadowColor = skColor.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnReject.skShadowOffset = new Point(1, 1);
            this.btnReject.skShowCaret = false;
            this.btnReject.skShowIcon = true;
            this.btnReject.skShowIconClose = false;
            this.btnReject.skShowIconMore = false;
            this.btnReject.skShowIconShadow = false;
            this.btnReject.skShowLineWhenCheck = false;
            this.btnReject.skShowNew = false;
            this.btnReject.skShowRedDot = false;
            this.btnReject.skShowToolTipOnButton = false;
            this.btnReject.skSplitString = "12,5,12,5";
            this.btnReject.skSuperviewReceiveMessage = true;
            this.btnReject.skTag = null;
            this.btnReject.skTagEx = null;
            this.btnReject.skText = "";
            this.btnReject.skTextAlign = ContentAlignment.TopLeft;
            this.btnReject.skTextColor = Color.White;
            this.btnReject.skTextColorCheck = Color.White;
            this.btnReject.skTextColorCheckDisable = Color.White;
            this.btnReject.skTextColorCheckDown = Color.White;
            this.btnReject.skTextColorCheckHover = Color.White;
            this.btnReject.skTextColorDisable = Color.White;
            this.btnReject.skTextColorDown = Color.White;
            this.btnReject.skTextColorHover = Color.White;
            this.btnReject.skTextFont = MyFont.CreateFont("微软雅黑", 9F);
            this.btnReject.skTextMouseDownPlace = 0;
            this.btnReject.skTextPlaceTip = 3;
            this.btnReject.skTextTemp = null;
            this.btnReject.skTheSameWidthOfTextAndIcon = false;
            this.btnReject.skTimerCountdownNumber = 0;
            this.btnReject.skToolTip = "";
            this.btnReject.skToolTipAlign = ContentAlignment.MiddleCenter;
            this.btnReject.skToolTipBackgroundColor = null;
            this.btnReject.skToolTipCheck = "";
            this.btnReject.skToolTipColor = Color.Gray;
            this.btnReject.skToolTipFont = MyFont.CreateFont("微软雅黑", 7F);
            this.btnReject.skTriangleHeight = 5;
            this.btnReject.skTrianglePlaceCorner = 15;
            this.btnReject.skTrianglePosition = skTrianglePosition.None;
            this.btnReject.skTriangleWidth = 12;
            this.btnReject.skUpToDownWhenCenter = true;
            this.btnReject.skUseDefaultIcon = false;
            this.btnReject.skViewParas = null;
            this.btnReject.skViewTypes = null;
            this.btnReject.TabIndex = 31;
            this.btnReject.Click += new System.EventHandler(this.btnReject_Click);
            // 
            // frmVoiceMsg
            // 
            //this.AutoScaleDimensions = new SizeF(6F, 12F);
            //this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Size = new Size(370, 112);
            this.Controls.Add(this.btnReject);
            this.Controls.Add(this.btnAgree);
            this.Controls.Add(this.lblUserDetail);
            this.Controls.Add(this.lblUserName);
            this.Controls.Add(this.picUserImage);
            this.Margin = new Padding(0);
            this.Name = "frmVoiceMsg";
            this.ShowInTaskbar = false;
            this.skBorderType = skBorderType.None;
            this.skShowButtonClose = false;
            this.skShowTitle = false;
            //this.skTransparentImageForm = MyResource.GetImage("rs_frm_bg_shadow.png");
            //this.skTransparentImageFormPadding = new Padding(4);
            this.Text = "frmVoiceMsg";
            //this.Controls.SetChildIndex(this.btnMin, 0);
            //this.Controls.SetChildIndex(this.btnMax, 0);
            //this.Controls.SetChildIndex(this.btnClose, 0);
            //this.Controls.SetChildIndex(this.picUserImage, 0);
            //this.Controls.SetChildIndex(this.lblUserName, 0);
            //this.Controls.SetChildIndex(this.lblUserDetail, 0);
            //this.Controls.SetChildIndex(this.btnAgree, 0);
            //this.Controls.SetChildIndex(this.btnReject, 0);
            this.ResumeLayout(false);

        }
    }
}
