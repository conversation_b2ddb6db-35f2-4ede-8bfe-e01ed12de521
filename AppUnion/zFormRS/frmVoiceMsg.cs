﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Diagnostics;
using System.Runtime.InteropServices;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;
using iTong.Android.Wave;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using Microsoft.Win32;
using iTong.CoreCefSharp;
#endif

namespace iTong.CoreModule
{
    public partial class frmVoiceMsg : frmNotify
    {
        private tdActionHelper<tdActionItemForRS> mActionHelper = tdActionHelper<tdActionItemForRS>.Instance();

        public override bool skShowLeftButton => false;
            

        private System.Timers.Timer mTimer;
        private int mTimerCount = 30;
            

        public frmVoiceMsg() : base()
        {
            InitializeComponent();
            this.skTransparentImageForNormal = MyResource.GetImage("rs_frm_bg_shadow.png");
            this.skTransparentImagePaddingForNormal = new Padding(4);

            this.skBorderRadius = new skBorderRadius(8);
            this.skTransparentImageSplitForNormal = new skSplit(15);

            SystemEvents.DisplaySettingsChanged += SystemEvents_DisplaySettingsChanged;
        }

        private void SystemEvents_DisplaySettingsChanged(object sender, EventArgs e)
        {
            this.RefreshSize();
            NotifyMgr.RefreshLocation();
        }

        protected override void InitControls()
        {
            base.InitControls();

            this.skTitleBarHeight = 0;

            if (this.TargetDevice != null && this.TargetDevice.user_info != null)
                this.lblUserName.skText = this.TargetDevice.user_info.mail;
            else
                this.lblUserName.skText = "";
            
            this.lblUserDetail.skText = this.Language.GetString("rs_requesting_chat_with_you");
            this.btnReject.skText = string.Format(this.Language.GetString("common_second"), this.mTimerCount);

            this.mTimer = new System.Timers.Timer();
            this.mTimer.Interval = 1000;
            this.mTimer.Elapsed += OnTimer_Elapsed;
            this.mTimer.Start();

            this.skCanbeMove = false;
            this.TopMost = true;

            this.RefreshSize();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
#if !MAC && !DPI
            this.SetUI();
#endif
        }

#if !MAC
        protected override void OnDpiChanged()
        {
            base.OnDpiChanged();

            this.skSourceSize = new Size(370, 112).ToDPI(this.Dpi);

            this.SetUI();
        }
#endif

        public override void SetUI()
        {
            base.SetUI();

            this.RefreshSize();
            NotifyMgr.RefreshLocation();
        }

#if MAC
        /// <summary>
        /// 重写RefreshRegion方法，frmVoiceMsg不需要绘制成不规则形状窗体
        /// </summary>
        public override void RefreshRegion()
        {
            if (this.Window == null)
                return;

            // 设置为普通的矩形窗体，不使用不规则形状
            this.Window.TitlebarAppearsTransparent = true;
            this.Window.BackgroundColor = NSColor.Clear;
            this.Window.IsOpaque = false;

            // 移除任何现有的mask，保持矩形形状
            this.Layer.Mask = null;
        }
#endif

        private void OnTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            this.mTimerCount -= 1;
            this.RequestButtonHandle();

            if (this.mTimerCount == 0)
            {
                this.SendVoIP(this.TargetDevice, skVoIPStatus.Timeout);

                frmConnect rsRemoteMsgForm = NotifyMgr.GetNotify<frmConnect>(RSEvent.Create(RSEventType.Connect, this.TargetDevice), NotifyType.RightBottom);
                if (rsRemoteMsgForm != null)
                {
                    rsRemoteMsgForm.VoiceHandle(false);
                    rsRemoteMsgForm.CommonTimeTip(RecordTipType.VoIPTimeoutBySelf);
                }
                this.Close();
            }
        }
        private void RequestButtonHandle()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() =>
                {
                    this.RequestButtonHandle();
                }));
            }
            else
            {
                this.btnReject.skText = string.Format(this.Language.GetString("common_second"), this.mTimerCount);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            SystemEvents.DisplaySettingsChanged -= SystemEvents_DisplaySettingsChanged;

            if (this.mTimer != null)
            {
                this.mTimer.Stop();
                this.mTimer = null;
            }
        }

        private void btnReject_Click(object sender, EventArgs e)
        {
            this.ConnectVoiceHandle(false);
        }

        private void btnAgree_Click(object sender, EventArgs e)
        {
            this.ConnectVoiceHandle(true);
        }

        private void ConnectVoiceHandle(bool isOpen)
        {
            if (!isOpen)
            {
                this.mActionHelper.Add(tdActionModeKeyForRS.ConnectVoIPReject);
                this.SendVoIP(this.TargetDevice, skVoIPStatus.Refuse);
            }
            else
            {
                this.mActionHelper.Add(tdActionModeKeyForRS.ConnectVoIPAccept);
                this.SendVoIP(this.TargetDevice, skVoIPStatus.Accept);
            }

            frmConnect rsRemoteMsgForm = NotifyMgr.GetNotify<frmConnect>(RSEvent.Create(RSEventType.Connect, this.TargetDevice), NotifyType.RightBottom);
            if (rsRemoteMsgForm != null)
            {
                rsRemoteMsgForm.VoiceHandle(isOpen);

                if (!isOpen)
                    rsRemoteMsgForm.CommonTimeTip(RecordTipType.VoIPRefuseBySelf);
                else
                    rsRemoteMsgForm.CommonTimeTip(RecordTipType.VoIPOpen);

            }
            this.Close();
        }


        private void SendVoIP(skDevice device, skVoIPStatus skVoIPStatus)
        {
            RSEvent arg = new RSEvent();
            arg.EventType = RSEventType.VoIP;
            arg.TargetDevice = device;
            arg.Code = (int)skVoIPStatus;
            SocketMgr.SendMsgFromClient(arg);
        }
    }
}
