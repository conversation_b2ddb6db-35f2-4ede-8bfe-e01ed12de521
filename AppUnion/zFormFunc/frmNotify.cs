using System;
using System.Windows.Forms;
using System.Text;
using System.IO;
using System.Collections.Generic;

using iTong.CoreFoundation;
using iTong.Android;
using System.Runtime.InteropServices;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
using CoreAnimation;
#else
using System.Drawing;
using System.Drawing.Drawing2D;
#endif

namespace iTong.CoreModule
{
    /// <summary>
    /// 通知窗体
    /// </summary>
    public partial class frmNotify : frmBase
    {
        /// <summary>是否需要通知</summary>
        public bool NeedNotify { get; set; } = true;
        /// <summary>通知位置</summary>
        public NotifyType NotifyType { get; set; } = NotifyType.RightBottom;
        /// <summary>目标设备</summary>
        public skDevice TargetDevice { get; set; }
        /// <summary>通知用户</summary>
        public skUserInfo User { get; set; }
        /// <summary>通知事件</summary>
        public RSEvent RSEvent { get; set; }

        /// <summary>窗口自动收起</summary>
        public virtual bool skAutoPackup { get; set; } = false;

        /// <summary>窗口自动收起时间，单位秒</summary>
        public virtual int skAutoPackupInterval { get; set; } = 0;

        #region 背景阴影


        /// <summary>
        /// 窗体折叠收藏起来时，窗体背景图片，可带阴影效果
        /// </summary>
        public virtual Image skTransparentImageForMini { get; set; }

        /// <summary>
        /// 窗体折叠收藏起来时，背景图片的九宫格切割
        /// </summary>
        public virtual skSplit skTransparentImageSplitForMini { get; set; } = skSplit.Empty;

        /// <summary>
        /// 窗体折叠收藏起来时，窗体上下左右的外沿阴影的边距
        /// </summary>
        public virtual Padding skTransparentImagePaddingForMini { get; set; } = Padding.Empty;



        /// <summary>
        /// 窗体展开时，窗体背景图片，可带阴影效果
        /// </summary>
        public virtual Image skTransparentImageForNormal { get; set; }

        /// <summary>
        /// 窗体展开时，背景图片的九宫格切割
        /// </summary>
        public virtual skSplit skTransparentImageSplitForNormal { get; set; } = skSplit.Empty;

        /// <summary>
        /// 窗体展开时，窗体上下左右的外沿阴影的边距
        /// </summary>
        public virtual Padding skTransparentImagePaddingForNormal { get; set; } = Padding.Empty;

        #endregion

        /// <summary>
        /// 窗口初始尺寸
        /// </summary>
        protected Size mSourceSize = Size.Empty;
        public Size skSourceSize
        {
            get { return this.mSourceSize; }
            set
            {
                this.mSourceSize = value;
                this.Size = value;
            }
        }

        /// <summary>
        /// 折叠迷你模式
        /// </summary>
        public bool skIsMiniMode { get; set; } = false;

        /// <summary>
        /// 显示左边是否折叠按钮
        /// </summary>
        public virtual bool skShowLeftButton { get; set; } = false;

        public virtual skBorderRadius skLeftButtonBorderRadius { get; set; } = new skBorderRadius(4, 0, 0, 4);

        /// <summary>
        /// 显示左边按钮尺寸
        /// </summary>
#if MAC
        public virtual Size skLeftButtonSize { get; set; } = new Size(20, 30);
#else
        public virtual Size skLeftButtonSize { get; set; } = new Size(22, 30);
#endif

        /// <summary>
        /// 显示左边按三角型高度差值
        /// </summary>
        public virtual int skLeftButtonHeightDiff { get; set; } = 6;

        /// <summary>
        /// 左边按钮展开图片
        /// </summary>
        public virtual Image skLeftButtonExpandImage { get; set; }

        /// <summary>
        /// 左边按钮展开图片是几态图
        /// </summary>
        public virtual skImageState skLeftButtonExpandImageState { get; set; }

        /// <summary>
        /// 左边按钮折叠图片
        /// </summary>
        public virtual Image skLeftButtonCollapseImage { get; set; }

        /// <summary>
        /// 左边按钮折叠图片是几态图
        /// </summary>
        public virtual skImageState skLeftButtonCollapseImageState { get; set; }

        private skButton btnAllow;
        private System.Timers.Timer mTimer;

        #region 移动窗体

        public bool IsDragged = false;

        private bool mNeedDrag = false;
        private Point mMouseDownLocation;

        /// <summary>
        /// 设置可移动区域
        /// </summary>
        public virtual Rectangle MoveRectangle { get; set; }

        #endregion

        public frmNotify() : base()
        {
            InitializeComponent();

            this.BackColor = Color.White;
            this.FormBorderStyle = FormBorderStyle.None;
            this.skBorderType = skBorderType.None;
            this.skShowTitle = false;
            this.ShowInTaskbar = false;
            this.skShowButtonClose = false;
            this.skShowButtonMax = false;
            this.skShowButtonMin = false;

            this.skBorderRadius = skBorderRadius.Empty;

            //this.skTransparentImageSplitForNormal = new skSplit(MyDpi.DpiHandle(38), MyDpi.DpiHandle(55), MyDpi.DpiHandle(55), MyDpi.DpiHandle(55));
            this.skTransparentImageSplitForNormal = new skSplit(38, 55, 55, 55);
            this.skTransparentImageSplitForMini = new skSplit(15, 17, 15, 17);

            this.skTransparentImageForNormal = MyResource.GetImage("rs_frm_shadow.png");
            this.skTransparentImageForMini = MyResource.GetImage("rs_frm_corner.png");

            this.skTransparentImagePaddingForNormal = new Padding(7, 8, 8, 8).ToDPI(this.Dpi);
            //this.skTransparentImagePaddingForNormal = new Padding(7, 9, 8, 8);
            this.skTransparentImagePaddingForMini = new Padding(8, 7, 0, 7).ToDPI(this.Dpi);

            this.skLeftButtonExpandImage = MyResource.GetImage("rs_arrow_right.png");
            this.skLeftButtonExpandImageState = skImageState.OneState;

            this.skLeftButtonCollapseImage = MyResource.GetImage("rs_arrow_left.png");
            this.skLeftButtonCollapseImageState = skImageState.OneState;
        }

#if MAC
        private void InitializeComponent()
        {

        }
#endif

        protected override void InitControls()
        {
            base.InitControls();
        }

        public virtual void SetRsEvent(RSEvent e)
        {
            this.RSEvent = e;

            if (e.TargetDevice != null)
            {
                this.TargetDevice = e.TargetDevice;

                if (e.TargetDevice.user_info != null)
                    this.User = e.TargetDevice.user_info;
            }
        }

        public virtual void SetUI()
        {

        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            this.InitClickRectangle();

            this.StartTimer();
#if MAC
            // 禁用窗口背景拖动，并注册窗口移动监听
            if (this.Window != null)
            {
                this.Window.MovableByWindowBackground = false;
                // 监听窗口移动事件，实时矫正位置
                this.mMoveObserver = NSNotificationCenter.DefaultCenter.AddObserver(
                    NSWindow.DidMoveNotification, 
                    OnWindowDidMove, 
                    this.Window
                );
            }
#endif
        }

        protected void StartTimer()
        {
            if (!this.skAutoPackup || this.skAutoPackupInterval <= 0)
                return;

            this.mTimer = new System.Timers.Timer();
            this.mTimer.Interval = this.skAutoPackupInterval;
            this.mTimer.Elapsed += OnTimer_Elapsed;
            this.mTimer.Start();
        }

        protected void StopTimer()
        {
            if (this.mTimer == null)
                return;

            this.mTimer.Stop();
            this.mTimer.Elapsed -= OnTimer_Elapsed;
            this.mTimer = null;
        }

        private void OnTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            if (this.skShowLeftButton && !this.skIsMiniMode && this.skAutoPackup)
            {
                this.btnAllow_Click(this.btnAllow, null);
            }

            this.StopTimer();
        }

        /// <summary>
        /// 初始化点击
        /// </summary>
        private void InitClickRectangle()
        {
            if (this.btnAllow != null)
                return;

            this.btnAllow = new skButton();
            this.btnAllow.Name = "btnClick";
            this.btnAllow.skBackgroundColor = skColor.Transparent;
            this.btnAllow.Size = new Size(this.skLeftButtonSize.Width, this.skLeftButtonSize.Height + this.skLeftButtonHeightDiff).ToDPI(this.Dpi);
            this.btnAllow.skText = "";
            this.btnAllow.Click += btnAllow_Click;
            this.btnAllow.MouseMove += BtnAllow_MouseMove;
            this.btnAllow.Anchor = AnchorStyles.Left | AnchorStyles.Top;
            this.btnAllow.Visible = this.skShowLeftButton;
#if MAC
            this.btnAllow.Location = new Point(10, this.Height - this.btnAllow.Height);
            this.btnAllow.MouseDowned += BtnAllow_MouseDown;
            this.btnAllow.MouseUped += BtnAllow_MouseUp;
#else
            this.btnAllow.Location = new Point(0, 0);
            this.btnAllow.MouseDown += BtnAllow_MouseDown;
            this.btnAllow.MouseUp += BtnAllow_MouseUp;
#endif

            this.Controls.Add(this.btnAllow);
        }

        protected virtual void BtnAllow_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.mNeedDrag = true;
                this.mMouseDownLocation = e.Location;
            }
        }

        protected virtual void BtnAllow_MouseMove(object sender, MouseEventArgs e)
        {
            if (!this.mNeedDrag || e.Button != MouseButtons.Left)
                return;

            /*
             * 算法思路：
             * 当前鼠标位置与鼠标down时的位置进行做差，若插值的绝对值大于5(即5px)则判定为拖动窗口
             * 在拖动完成后对位置进行调正，使其靠右边
             */

            int deltaX = (int)(e.Location.X - this.mMouseDownLocation.X);
            int deltaY = (int)(e.Location.Y - this.mMouseDownLocation.Y);

            int threshold = 5;

            if (Math.Abs(deltaX) > threshold || Math.Abs(deltaY) > threshold)
            {
                this.IsDragged = true;

#if !MAC
                MyAPI.ReleaseCapture();
                MyAPI.SendMessage(this.Handle, (int)SK_WindowMessage.WM_NCLBUTTONDOWN, (int)SK_WindowMessage.WM_DESTROY, 0);
#endif
            }
        }

        protected virtual void BtnAllow_MouseUp(object sender, MouseEventArgs e)
        {
            this.mNeedDrag = false;
        }

#if MAC
        private NSObject mMoveObserver;

        private void OnWindowDidMove(NSNotification notification)
        {
            if (this.Window == null)
                return;

            // 获取屏幕可见区域
            Rectangle rect = NSScreen.MainScreen.VisibleFrame;
            int right = (int)rect.Right;
            int miniOffset = this.skIsMiniMode ? 10 : 0;

            var frame = this.Window.Frame;

            // 计算正确的右侧位置
            nfloat correctX = right - frame.Width + miniOffset;
            nfloat currentY = frame.Y;

            // 根据NotifyType限制Y坐标范围
            if (this.NotifyType == NotifyType.RightTop)
            {
                // RightTop窗口应该在屏幕上半部分
                if (currentY < rect.Top + 57)
                    currentY = rect.Top + 57;
                else if (currentY > rect.Top + rect.Height / 2)
                    currentY = rect.Top + rect.Height / 2;
            }
            else if (this.NotifyType == NotifyType.RightBottom)
            {
                // RightBottom窗口应该在屏幕下半部分
                if (currentY < rect.Top + rect.Height / 2)
                    currentY = rect.Top + rect.Height / 2;
                else if (currentY + frame.Height > rect.Bottom)
                    currentY = rect.Bottom - frame.Height;
            }
            else
            {
                // 默认限制在屏幕范围内
                if (currentY < rect.Top)
                    currentY = rect.Top;
                else if (currentY + frame.Height > rect.Bottom)
                    currentY = rect.Bottom - frame.Height;
            }

            // 如果X坐标不正确，立即矫正到右侧
            if (Math.Abs(frame.X - correctX) > 1)
            {
                this.LocationForWindow = new Point(correctX, currentY);
                Console.WriteLine($"OnWindowDidMove: {this.GetType().Name} ({this.NotifyType}) 矫正位置为 {new Point(correctX, currentY)}");
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 清理监听器
            if (this.mMoveObserver != null)
            {
                NSNotificationCenter.DefaultCenter.RemoveObserver(this.mMoveObserver);
                this.mMoveObserver = null;
            }
            base.OnFormClosing(e);
        }
#endif

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        protected override void WndProc(ref Message m)
        {
#if !MAC
            if ((MoveRectangle.Left != 0 || MoveRectangle.Top != 0 || MoveRectangle.Right != 0 || MoveRectangle.Bottom != 0))
            {
                if (m.Msg == 0x0216)
                {
                    // 获取当前窗口的边界
                    RECT rect = (RECT)Marshal.PtrToStructure(m.LParam, typeof(RECT));
                    
                    // 水平始终贴右侧：锁定 Right，仅允许上下移动
                    int widthNow = rect.Right - rect.Left;
                    rect.Right = MoveRectangle.Right;
                    rect.Left = rect.Right - widthNow;

                    // 限制窗口垂直方向移动区域
                    if (rect.Top < MoveRectangle.Top)
                    {
                        int offset = MoveRectangle.Top - rect.Top;
                        rect.Top += offset;
                        rect.Bottom += offset;
                    }
                    if (rect.Bottom > MoveRectangle.Bottom)
                    {
                        int offset = rect.Bottom - MoveRectangle.Bottom;
                        rect.Top -= offset;
                        rect.Bottom -= offset;
                    }

                    // 更新窗口位置
                    Marshal.StructureToPtr(rect, m.LParam, true);
                }
            }
#endif
            base.WndProc(ref m);
        }

        private void btnAllow_Click(object sender, System.EventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new System.Threading.ThreadStart(() =>
                {
                    btnAllow_Click(sender, e);
                }));
            }
            else
            {
                if (this.skShowLeftButton)
                {
                    this.skIsMiniMode = !this.skIsMiniMode;
#if MAC
                    this.SetBtnDisconnectVisible();
#endif
                    this.RefreshSize();
                }
            }
        }



#if MAC
        public virtual void SetBtnDisconnectVisible()
        {

        }
#endif

        /// <summary>
        /// 刷新绘画尺寸
        /// </summary>
        /// <param name="refreshLocation">是否重新定位</param>
        public void RefreshSize(bool refreshLocation = true)
        {
            Point viewLocation = this.Location;
            Size viewSize = this.Size;

#if MAC
            if (this.Window != null)
            {
                viewLocation = this.Window.Frame.Location;
            }

            Rectangle rect = NSScreen.MainScreen.VisibleFrame;

            int windowRadius = 10;
#else
            Rectangle rect = Screen.PrimaryScreen.WorkingArea;
#endif
            int right = (int)(rect.Right);

            bool topMost = this.TopMost;

            this.TopMost = false;


            if (this.skIsMiniMode)//折叠迷你模式
            {
                if (this.skLeftButtonCollapseImage != null && this.btnAllow != null)
                {
                    this.btnAllow.skIcon = this.skLeftButtonCollapseImage;
                    this.btnAllow.skIconState = this.skLeftButtonCollapseImageState;
                    this.btnAllow.skIconOffset = new Point(2, 1);
                    this.btnAllow.skIconSize = new Size(8, 14);
                }
#if MAC
                Size newSize = new Size(this.skLeftButtonSize.Width + windowRadius * 2, this.skLeftButtonSize.Height + this.skLeftButtonHeightDiff); //因为this.Window有默认10px的圆角会影响不规则窗体的绘制，所以左右两边各增加10px的宽度并处理成透明
                this.SizeForWindow = newSize;
#else
                Size newSize = new Size(this.skLeftButtonSize.Width, this.skLeftButtonSize.Height + this.skLeftButtonHeightDiff).ToDPI(this.Dpi);
                this.Size = newSize;
#endif

                this.SetTransparentImage(this.skTransparentImageForMini, this.skTransparentImageSplitForMini, this.skTransparentImagePaddingForMini);
            }
            else//展开模式
            {
                if (this.skLeftButtonExpandImage != null && this.btnAllow != null)
                {
                    this.btnAllow.skIcon = this.skLeftButtonExpandImage;
                    this.btnAllow.skIconState = this.skLeftButtonExpandImageState;
                    this.btnAllow.skIconOffset = new Point(2, 1);
                    this.btnAllow.skIconSize = new Size(8, 14);
                }
#if MAC
                this.SizeForWindow = this.skSourceSize;
#else
                this.Size = this.skSourceSize;
#endif

                this.SetTransparentImage(this.skTransparentImageForNormal, this.skTransparentImageSplitForNormal, this.skTransparentImagePaddingForNormal);
            }

#if MAC
            int miniOffset = this.skIsMiniMode ? 10 : 0;
#else
            int miniOffset = 0;
#endif
            this.MoveRectangle = new Rectangle(right - this.Width + miniOffset, rect.Top, this.Width, rect.Bottom);

            if (refreshLocation)
            {
                int y = 0;

#if MAC
                y = (int)viewLocation.Y + (int)viewSize.Height - this.Height;

                if (y < rect.Top)
                    y = (int)rect.Top;
                else if (y + this.Height > rect.Bottom)
                    y = (int)(rect.Bottom - this.Height);

                int locationX = right - this.Width;

                if (this.skIsMiniMode)//折叠迷你模式
                    locationX += windowRadius; //mac窗体有默认圆角，所以增加10px宽度，达到右边是一个矩形效果

                this.LocationForWindow = new Point(locationX, y);
#else
                y = (int)viewLocation.Y;
                if (viewLocation.Y + this.Height > rect.Bottom)
                    y = (int)(rect.Bottom - this.Height);

                this.Location = new Point(right - this.Width + miniOffset, y);
#endif
            }

#if !MAC
            this.RefreshRegion();
            this.ShowShadow();

            if (this is frmConnect)
            {
                using (SolidBrush color = new SolidBrush(this.skTitleBackgroundColor))
                {
                    using (GraphicsPath getNotifyRegion = GetNotifyRegion())
                    {
                        this.SetCustomGraphics(getNotifyRegion, color);
                    }
                }
            }

#else
            this.RefreshRegion();
#endif

            this.TopMost = topMost;
            this.Activate();
        }

#if MAC
        public override void RefreshRegion()
        {
            if (this.Window == null)
                return;

            this.Window.TitlebarAppearsTransparent = true;  // 设置标题栏透明
            this.Window.BackgroundColor = NSColor.Clear; // 设置标题栏为透明背景
            this.Window.IsOpaque = false; // 确保窗口不透明度设置正确

            if (this.skIsMiniMode)
            {
                // 定义尺寸
                float width = 30;
                float height = 36;
                float radius = 6;
                float offsetHeight = 4;
                int startX = 10;

                var path = new CGPath();

                path.MoveToPoint(startX, radius + offsetHeight); // 起点：左下圆角起始点 (startX, radius + offsetHeight)

                path.AddQuadCurveToPoint(startX, offsetHeight, 13, offsetHeight); // 左下角圆弧

                path.AddLineToPoint(width, 0); // 底部斜边

                path.AddLineToPoint(width, height); // 右边

                path.AddLineToPoint(startX + radius, height); // 顶边

                path.AddArcToPoint(startX, height, startX, height - radius, radius); // 左上角圆弧

                // 闭合路径
                path.CloseSubpath();

                var shapeLayer = new CAShapeLayer();
                shapeLayer.Path = path;
                this.Layer.Mask = shapeLayer;
            }
            else
            {
                this.Window.BackgroundColor = NSColor.Clear; // 设置透明背景
                this.Window.IsOpaque = false;

                nfloat width = this.Size.Width;
                nfloat height = this.Size.Height;
                float cornerRadius = 10;

                float cutWidth = 30; // btnAllow宽度20px + Window默认的radius 10px
                nfloat cutHeight = this.Size.Height - 36;

                var path = new CGPath();

                path.MoveToPoint(cutWidth, cutHeight); // 开始绘制路径 - 从切除区域的右侧开始

                path.AddLineToPoint(cutWidth, cornerRadius); // 左下部分 侧边

                path.AddArcToPoint(cutWidth, 0, cutWidth + cornerRadius, 0, cornerRadius); // 左下部分 圆角

                path.AddLineToPoint(width - cornerRadius, 0); // 底边

                path.AddArcToPoint(width, 0, width, cornerRadius, cornerRadius); // 右下圆角

                path.AddLineToPoint(width, height - cornerRadius); // 右边

                path.AddArcToPoint(width, height, width - cornerRadius, height, cornerRadius); // 右上圆角

                cornerRadius = 6; //btnAllow 圆角改成6px

                path.AddLineToPoint(cornerRadius + 10, height); // 顶边

                path.AddArcToPoint(10, height, 10, height - cornerRadius, cornerRadius); // 左上部 上圆角

                path.AddLineToPoint(10, cutHeight + cornerRadius + 4); // 左上部 侧边

                path.AddQuadCurveToPoint(10, cutHeight + 4, 13, cutHeight + 4); //左上部 下圆角

                // 闭合路径
                path.CloseSubpath();

                var shapeLayer = new CAShapeLayer();
                shapeLayer.Path = path;
                this.Layer.Mask = shapeLayer;
            }
        }

        protected void SetTransparentImage(NSImage btnImage, skSplit btnSplit, Padding btnPadding)
        {

        }
#else
        /// <summary>
        /// 重置绘画区域
        /// </summary>
        public override void RefreshRegion()
        {
            this.Region = null;

            GraphicsPath pathForm = new GraphicsPath();

            if (this.DesignMode)
            {
                //设计器模式，直接使用窗口大小
                pathForm = skGuiHelper.CreateRoundPath(this.ClientRectangle, this.skBorderRadius, penWidth: 0);
            }
            else
            {
                int btnWidth = 0;
                //显示折叠按钮
                if (this.skShowLeftButton)
                {
                    btnWidth = MyDpi.ToDPI(this.skLeftButtonSize.Width, this.Dpi);

                    //添加左不规则按钮
                    Rectangle rectLeft = new Rectangle(0, 0, btnWidth, MyDpi.ToDPI(this.skLeftButtonSize.Height, this.Dpi));
                    GraphicsPath pathLeft = skGuiHelper.CreateRoundPath(rectLeft, this.skLeftButtonBorderRadius, MyDpi.ToDPI(this.skLeftButtonHeightDiff, this.Dpi), penWidth: 0);
                    pathForm.AddPath(pathLeft, true);//添加的路径中的第一个图形是该路径中最后一个图形的一部分
                    pathLeft.Dispose();
                }

                //非折叠模式
                if (!this.skIsMiniMode)
                {
                    //添加右侧矩形窗口
                    int diffX = 1;
                    Rectangle rectRight = new Rectangle(btnWidth - diffX, 0, this.Width - btnWidth + diffX, this.Height);
                    GraphicsPath pathRight = skGuiHelper.CreateRoundPath(rectRight, this.skBorderRadius, penWidth: 0);
                    pathForm.AddPath(pathRight, true);//添加的路径中的第一个图形是该路径中最后一个图形的一部分
                    pathRight.Dispose();
                }
            }

            this.Region = new Region(pathForm);
            pathForm.Dispose();
        }

        private GraphicsPath GetNotifyRegion()
        {
            GraphicsPath pathForm = new GraphicsPath();

            if (this.DesignMode)
            {
                //设计器模式，直接使用窗口大小
                pathForm = skGuiHelper.CreateRoundPath(this.ClientRectangle, this.skBorderRadius, penWidth: 0);
            }
            else
            {
                int btnWidth = 0;
                //显示折叠按钮
                if (this.skShowLeftButton)
                {
                    btnWidth = MyDpi.ToDPI(this.skLeftButtonSize.Width, this.Dpi);

                    //添加左不规则按钮
                    Rectangle rectLeft = new Rectangle(MyDpi.ToDPI(7, this.Dpi), MyDpi.ToDPI(9, this.Dpi), btnWidth, MyDpi.ToDPI(this.skLeftButtonSize.Height - 1, this.Dpi));
                    if (this.skIsMiniMode)
                        rectLeft = new Rectangle(MyDpi.ToDPI(8, this.Dpi), MyDpi.ToDPI(7, this.Dpi), btnWidth, MyDpi.ToDPI(this.skLeftButtonSize.Height-1, this.Dpi));

                    GraphicsPath pathLeft = skGuiHelper.CreateRoundPath(rectLeft, this.skLeftButtonBorderRadius, MyDpi.ToDPI(this.skLeftButtonHeightDiff, this.Dpi), penWidth: 0);
                    pathForm.AddPath(pathLeft, true);//添加的路径中的第一个图形是该路径中最后一个图形的一部分
                    pathLeft.Dispose();
                }
            }

            return pathForm;
        }

#endif
    }
}
