﻿using System;
using System.Windows.Forms;
using System.Text;
using System.IO;
using System.Collections.Generic;

using iTong.CoreFoundation;
using iTong.Android;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using System.Drawing.Drawing2D;
#endif

namespace iTong.CoreModule
{
    /// <summary>
    /// 通知位置
    /// </summary>
    public enum NotifyType
    {
        /// <summary>
        /// 屏幕右下角
        /// </summary>
        RightBottom,

        /// <summary>
        /// 屏幕右上角
        /// </summary>
        RightTop,
    }

    /// <summary>
    /// 通知行为
    /// </summary>
    public enum NotifyAction
    {
        Get,
        Add,
        Remove,
        RemoveDict,
    }

    /// <summary>
    /// 用于通知管理
    /// </summary>
    public class NotifyMgr
    {
        /// <summary>同步锁</summary>
        private static object mLocker = new object();
        /// <summary>用于存储通知窗体的字典</summary>
        private static Dictionary<string, frmNotify> mDictNotify = new Dictionary<string, frmNotify>();
        /// <summary>用于存储通知窗体的列表</summary>
        private static List<frmNotify> mListNotify = new List<frmNotify>();

        /// <summary>
        /// 获取key
        /// </summary>
        /// <param name="e">RsEvent对象</param>
        /// <returns>事件类型+设备唯一码</returns>
        public static string GetKey(RSEvent e)
        {
            RSEventType type = e.EventType;

            if (type == RSEventType.ImageRecvSuccess ||
                type == RSEventType.SecurityAuthSuccessed ||
                type == RSEventType.CloseWindows ||
                type == RSEventType.Disconnect)
            {
                type = RSEventType.Connect;
            }

            string unique_id = e.TargetDevice == null ? "" : e.TargetDevice.unique_id;
            string strKey = string.Format("{0}_{1}", type.ToString(), unique_id);

            return strKey;
        }

        /// <summary>
        /// 添加、移除
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="action">通知行为</param>
        /// <param name="e">Rs事件</param>
        /// <param name="pos">通知位置</param>
        /// <param name="reOpen">重新打开</param>
        /// <returns>frmNotify对象</returns>
        private static T AddOrRemove<T>(NotifyAction action, RSEvent e, NotifyType pos, bool reOpen = false) where T : frmNotify
        {
            T frm = null;

            lock (mLocker)
            {
                try
                {
                    string strKey = GetKey(e);

                    if (action == NotifyAction.Get)
                    {
                        if (mDictNotify.ContainsKey(strKey))
                        {
                            frm = (T)mDictNotify[strKey];
                            if (frm.IsDisposed)
                            {
                                mDictNotify.Remove(strKey);
                                frm = null;
                            }
                        }
                    }
                    else if (action == NotifyAction.Add)
                    {
                        if (mDictNotify.ContainsKey(strKey))
                        {
                            frm = (T)mDictNotify[strKey];
                            if (frm.IsDisposed)
                            {
                                mDictNotify.Remove(strKey);
                                frm = null;
                            }
                            else if (frm != null && reOpen)
                            {
                                CloseNotify(e, pos);
                                frm = null;
                            }
                        }

                        if (frm == null)
                        {
                            frm = MyClass2.CreateInstanceT<T>();
                            frm.NotifyType = pos;
                            frm.FormClosing += OnNotifyFormClosing;

                            mDictNotify[strKey] = frm;

                            mListNotify.Add(frm);
                        }

                        frm.SetRsEvent(e);

                        //frm.SetUI();

                        //RefreshLocation();
                    }
                    else
                    {
                        if (mDictNotify.ContainsKey(strKey))
                        {
                            frm = (T)mDictNotify[strKey];

                            //先移除再调整关闭窗口
                            mListNotify.Remove(frm);
                            mDictNotify.Remove(strKey);

                            if (action != NotifyAction.RemoveDict)
                            {
                                frm.NeedNotify = false;
                                frm.FormClosing -= OnNotifyFormClosing;
                                frm.Close();
                            }

                            RefreshLocation();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "NotifyMgr.AddOrRemove");
                }
            }

            return frm;
        }

        /// <summary>
        /// 在通知关闭的时候
        /// </summary>
        private static void OnNotifyFormClosing(object sender, FormClosingEventArgs e)
        {
            frmNotify frm = sender as frmNotify;
            if (frm == null)
                return;

            if (frm.NeedNotify)
                NotifyMgr.CloseNotifyDict(frm.RSEvent, frm.NotifyType);
        }

        /// <summary>
        /// 刷新位置
        /// </summary>
        public static void RefreshLocation()
        {
            try
            {
#if MAC
                Rectangle rect = NSScreen.MainScreen.VisibleFrame;
                nfloat y = rect.Top;
                nfloat x = rect.Right;
#else
                Rectangle rect = Screen.PrimaryScreen.WorkingArea;
                int y = (int)rect.Bottom;
                int x = (int)rect.Right;
#endif

                int diffY = 20;//竖向间隔20像素

                for (int i = mListNotify.Count - 1; i >= 0; i--)
                {
                    frmNotify frm = mListNotify[i];
                    if (frm.NotifyType != NotifyType.RightBottom)
                        continue;

                    Size frmSize = frm.Size;

                    frm.TopMost = false;
#if MAC
                    frm.LocationForWindow = new Point(x - frm.Size.Width, y);
                    y = y + frmSize.Height + diffY;
#else
                    // 当刚开局还未移动时窗体位置都是更新的
                    if (frm.IsDragged)
                        continue;

                    frm.Location = new Point(x - frm.Size.Width, y - frmSize.Height);

                    y = y - frmSize.Height - diffY;
#endif
                    frm.TopMost = true;
                    frm.Activate();
                }

#if MAC
                y = rect.Bottom;
#else
                y = rect.Top;
#endif

                y = y + 57; //不要太置顶，下移57像素
                x = x - 20;  //不要太顶边，左移20像素

                for (int i = mListNotify.Count - 1; i >= 0; i--)
                {
                    frmNotify frm = mListNotify[i];
                    if (frm.NotifyType != NotifyType.RightTop)
                        continue;

                    Size frmSize = frm.Size;

                    frm.TopMost = false;
#if MAC
                    frm.LocationForWindow = new Point(x - frm.Size.Width, y - frmSize.Height - diffY);

                    y = y - (frmSize.Height + diffY);
#else
                    frm.Location = new Point(x - frm.Size.Width, y + diffY);
                    y = y + frmSize.Height + diffY;
#endif
                    frm.TopMost = true;
                    frm.Activate();
                }
            }
            catch
            { }
        }

        /// <summary>
        /// 获取通知
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="e">Rs事件</param>
        /// <param name="pos">通知位置</param>
        /// <returns>frmNotify对象</returns>
        public static T GetNotify<T>(RSEvent e, NotifyType pos = NotifyType.RightBottom) where T : frmNotify
        {
            T frm = AddOrRemove<T>(NotifyAction.Get, e, pos);

            return frm;
        }

        /// <summary>
        /// 打开通知
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="e">Rs事件</param>
        /// <param name="pos">通知位置</param>
        /// <returns>frmNotify对象</returns>
        public static T OpenNotify<T>(RSEvent e, NotifyType pos = NotifyType.RightBottom) where T : frmNotify
        {
            return OpenNotify<T>(e, pos, false);
        }

        /// <summary>
        /// 打开通知
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="e">Rs事件</param>
        /// <param name="pos">通知位置</param>
        /// <param name="reOpen">是否重开通知</param>
        /// <returns>frmNotify对象</returns>
        public static T OpenNotify<T>(RSEvent e, NotifyType pos = NotifyType.RightBottom, bool reOpen = false) where T : frmNotify
        {
            T frm = AddOrRemove<T>(NotifyAction.Add, e, pos, reOpen);

            if (frm != null)
            {
                frm.Show();
                frm.TopMost = true;
                frm.Activate();
            }

            return frm;
        }

        /// <summary>
        /// 关闭通知
        /// </summary>
        /// <param name="e">Rs事件</param>
        /// <param name="pos">通知位置</param>
        public static void CloseNotify(RSEvent e, NotifyType pos)
        {
            AddOrRemove<frmNotify>(NotifyAction.Remove, e, pos);
        }

        /// <summary>
        /// 关闭通知
        /// </summary>
        /// <param name="e">Rs事件</param>
        /// <param name="pos">通知位置</param>
        public static void CloseNotifyDict(RSEvent e, NotifyType pos)
        {
            AddOrRemove<frmNotify>(NotifyAction.RemoveDict, e, pos);
        }

        /// <summary>
        /// 清除所有通知
        /// </summary>
        public static void ClearNotify()
        {
            try
            {
                foreach (frmNotify frm in mListNotify)
                    frm.Close();

                mListNotify.Clear();
                mDictNotify.Clear();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "NotifyMgr.ClearNotify");
            }
        }

        /// <summary>
        /// 设置全部通知是否置顶显示
        /// </summary>
        /// <param name="topmost">是否置顶</param>
        public static void HasTopMostNotify(bool topmost)
        {
            try
            {
                foreach (frmNotify frm in mListNotify)
                {
                    frm.TopMost = topmost;
                    if (topmost)
                        frm.Activate();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "NotifyMgr.HasTopMostNotify");
            }
        }
    }
}
